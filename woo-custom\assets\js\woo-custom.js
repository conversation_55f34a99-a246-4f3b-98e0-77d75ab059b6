/**
 * Woo Custom Plugin JavaScript
 */

(function($) {
    'use strict';

    // Initialize when document is ready
    $(document).ready(function() {
        WooCustom.init();
    });

    // Main WooCustom object
    window.WooCustom = {
        
        // Initialize the plugin
        init: function() {
            this.bindEvents();
            this.initWishlistButtons();
            this.initCourseCompletedCheck();
        },

        // Bind event handlers
        bindEvents: function() {
            // Wishlist button clicks
            $(document).on('click', '.woo-custom-wishlist-btn', this.handleWishlistClick);
            
            // Remove from wishlist in wishlist page
            $(document).on('click', '.remove-from-wishlist', this.handleRemoveFromWishlist);
            
            // Handle AJAX cart additions on wishlist page
            $(document).on('added_to_cart', this.handleAddedToCart);
        },

        // Initialize wishlist buttons
        initWishlistButtons: function() {
            $('.woo-custom-wishlist-btn').each(function() {
                var $btn = $(this);
                var productId = $btn.data('product-id');
                
                // Set initial ARIA attributes
                $btn.attr('aria-pressed', $btn.hasClass('in-wishlist') ? 'true' : 'false');
                $btn.attr('aria-label', $btn.hasClass('in-wishlist') ? 
                    woo_custom_ajax.i18n.removed_from_wishlist : 
                    woo_custom_ajax.i18n.added_to_wishlist
                );
            });
        },

        // Handle wishlist button clicks
        handleWishlistClick: function(e) {
            e.preventDefault();
            e.stopPropagation();

            var $btn = $(this);
            var productId = $btn.data('product-id');
            var courseId = $btn.data('course-id');
            var itemType = $btn.data('item-type') || 'product';

            // Debug logging
            if (typeof console !== 'undefined' && console.log) {
                console.log('Woo Custom: Wishlist button clicked for', itemType, 'ID:', productId || courseId);
            }

            // Validate required data
            if (!productId && !courseId) {
                console.error('Woo Custom: No item ID found on button');
                WooCustom.showNotification('Item ID not found', 'error');
                return;
            }

            if (typeof woo_custom_ajax === 'undefined') {
                console.error('Woo Custom: woo_custom_ajax object not found');
                WooCustom.showNotification('Configuration error', 'error');
                return;
            }

            if (!woo_custom_ajax.ajax_url) {
                console.error('Woo Custom: AJAX URL not found');
                WooCustom.showNotification('AJAX URL not configured', 'error');
                return;
            }

            if (!woo_custom_ajax.nonce) {
                console.error('Woo Custom: Security nonce not found');
                WooCustom.showNotification('Security token missing', 'error');
                return;
            }

            // Prevent double clicks
            if ($btn.hasClass('loading')) {
                return;
            }

            // Add loading state
            $btn.addClass('loading');

            // Prepare AJAX data
            var data = {
                action: 'woo_custom_toggle_wishlist',
                nonce: woo_custom_ajax.nonce
            };

            // Add appropriate ID based on item type
            if (courseId) {
                data.course_id = courseId;
            } else {
                data.product_id = productId;
            }

            if (typeof console !== 'undefined' && console.log) {
                console.log('Woo Custom: Sending AJAX request with data:', data);
            }

            // Send AJAX request
            $.ajax({
                url: woo_custom_ajax.ajax_url,
                type: 'POST',
                data: data,
                timeout: 10000, // 10 second timeout
                success: function(response) {
                    if (typeof console !== 'undefined' && console.log) {
                        console.log('Woo Custom: AJAX response received:', response);
                    }
                    WooCustom.handleWishlistResponse(response, $btn, productId);
                },
                error: function(xhr, status, error) {
                    console.error('Woo Custom: AJAX error:', {
                        status: status,
                        error: error,
                        responseText: xhr.responseText,
                        statusCode: xhr.status
                    });

                    var errorMessage = woo_custom_ajax.i18n.error || 'An error occurred';
                    if (xhr.status === 0) {
                        errorMessage = 'Network error - please check your connection';
                    } else if (xhr.status === 404) {
                        errorMessage = 'AJAX endpoint not found';
                    } else if (xhr.status === 500) {
                        errorMessage = 'Server error - please try again';
                    }

                    WooCustom.showNotification(errorMessage, 'error');
                },
                complete: function() {
                    $btn.removeClass('loading');
                }
            });
        },

        // Handle wishlist AJAX response
        handleWishlistResponse: function(response, $btn, productId) {
            if (response.success) {
                var action = response.data.action;
                var message = response.data.message;
                
                // Update button state
                if (action === 'added') {
                    $btn.addClass('in-wishlist');
                    $btn.attr('aria-pressed', 'true');
                    $btn.attr('aria-label', woo_custom_ajax.i18n.removed_from_wishlist);
                } else {
                    $btn.removeClass('in-wishlist');
                    $btn.attr('aria-pressed', 'false');
                    $btn.attr('aria-label', woo_custom_ajax.i18n.added_to_wishlist);
                }
                
                // Update all buttons for this product
                $('.woo-custom-wishlist-btn[data-product-id="' + productId + '"]').each(function() {
                    var $otherBtn = $(this);
                    if (action === 'added') {
                        $otherBtn.addClass('in-wishlist');
                        $otherBtn.attr('aria-pressed', 'true');
                    } else {
                        $otherBtn.removeClass('in-wishlist');
                        $otherBtn.attr('aria-pressed', 'false');
                    }
                });
                
                // Show notification
                this.showNotification(message, 'success');
                
                // Update wishlist count if element exists
                this.updateWishlistCount(response.data.count);
                
            } else {
                // Handle error
                var errorMessage = response.data && response.data.message ? 
                    response.data.message : woo_custom_ajax.i18n.error;
                
                this.showNotification(errorMessage, 'error');
                
                // Redirect to login if needed
                if (response.data && response.data.redirect) {
                    setTimeout(function() {
                        window.location.href = response.data.redirect;
                    }, 2000);
                }
            }
        },

        // Handle remove from wishlist on wishlist page
        handleRemoveFromWishlist: function(e) {
            e.preventDefault();

            var $btn = $(this);
            var $row = $btn.closest('.wishlist-item');
            var productId = $btn.data('product-id');

            // Add loading state
            $btn.addClass('loading');
            
            // Prepare AJAX data
            var data = {
                action: 'woo_custom_toggle_wishlist',
                product_id: productId,
                nonce: woo_custom_ajax.nonce
            };
            
            // Send AJAX request
            $.ajax({
                url: woo_custom_ajax.ajax_url,
                type: 'POST',
                data: data,
                success: function(response) {
                    if (response.success && response.data.action === 'removed') {
                        // Animate row removal
                        $row.fadeOut(300, function() {
                            $row.remove();
                            
                            // Check if wishlist is now empty
                            if ($('.wishlist-item').length === 0) {
                                location.reload();
                            }
                        });
                        
                        // Update all buttons for this product
                        $('.woo-custom-wishlist-btn[data-product-id="' + productId + '"]').each(function() {
                            $(this).removeClass('in-wishlist').attr('aria-pressed', 'false');
                        });
                        
                        // Show notification
                        WooCustom.showNotification(response.data.message, 'success');
                        
                        // Update wishlist count
                        WooCustom.updateWishlistCount(response.data.count);
                    } else {
                        WooCustom.showNotification(woo_custom_ajax.i18n.error, 'error');
                    }
                },
                error: function() {
                    WooCustom.showNotification(woo_custom_ajax.i18n.error, 'error');
                },
                complete: function() {
                    $btn.removeClass('loading');
                }
            });
        },

        // Handle added to cart event
        handleAddedToCart: function(e, fragments, cart_hash, $button) {
            // You can add custom logic here when items are added to cart from wishlist
        },

        // Show notification
        showNotification: function(message, type) {
            type = type || 'success';
            
            // Remove existing notifications
            $('.woo-custom-notification').remove();
            
            // Create notification element
            var $notification = $('<div class="woo-custom-notification ' + type + '">' + message + '</div>');
            
            // Add to page
            $('body').append($notification);
            
            // Show notification
            setTimeout(function() {
                $notification.addClass('show');
            }, 100);
            
            // Hide notification after 3 seconds
            setTimeout(function() {
                $notification.removeClass('show');
                setTimeout(function() {
                    $notification.remove();
                }, 300);
            }, 3000);
        },

        // Update wishlist count
        updateWishlistCount: function(count) {
            // Update wishlist count in navigation or other elements
            $('.wishlist-count-number').text(count);
            
            // Update wishlist count text
            var $countText = $('.wishlist-count');
            if ($countText.length && count !== undefined) {
                if (count > 0) {
                    var text = count === 1 ? 
                        'You have 1 item in your wishlist' : 
                        'You have ' + count + ' items in your wishlist';
                    $countText.text(text);
                } else {
                    $countText.text('Your wishlist is empty');
                }
            }
        },

        // Utility function to get product ID from various contexts
        getProductId: function($element) {
            // Try different methods to get product ID
            var productId = $element.data('product-id') ||
                           $element.closest('[data-product-id]').data('product-id') ||
                           $element.closest('.product').find('[data-product_id]').data('product_id');

            return productId;
        },

        // Initialize course completed check
        initCourseCompletedCheck: function() {
            // Additional check for course completion on page load
            this.checkAndHideAddToCartButton();

            // Monitor for dynamic content changes
            var observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    if (mutation.type === 'childList') {
                        WooCustom.checkAndHideAddToCartButton();
                    }
                });
            });

            // Start observing
            if (document.querySelector('.summary')) {
                observer.observe(document.querySelector('.summary'), {
                    childList: true,
                    subtree: true
                });
            }
        },

        // Check and hide add to cart button if needed
        checkAndHideAddToCartButton: function() {
            // Check if we're on a single product page
            if (!$('body').hasClass('single-product')) {
                return;
            }

            // Check if course completed notice exists (indicates completed course)
            if ($('.course-completed-notice').length > 0) {
                this.hideAddToCartButton();
            }
        },

        // Hide add to cart button with specific selector
        hideAddToCartButton: function() {
            // Hide the specific button mentioned by user
            $("#sticky-scroll > button").hide();

            // Hide other common add to cart buttons as fallback
            $(".single_add_to_cart_button").hide();
            $("button[name='add-to-cart']").hide();
            $(".cart button[type='submit']").hide();

            // Add body class for CSS targeting
            $('body').addClass('course-completed');

            // Log for debugging
            if (typeof console !== 'undefined' && console.log) {
                console.log('Woo Custom: Add to cart button hidden for completed course');
            }
        }
    };

    // Handle WooCommerce fragments update (for cart updates)
    $(document.body).on('updated_wc_div', function() {
        // Reinitialize wishlist buttons after fragments update
        WooCustom.initWishlistButtons();
    });

    // Handle infinite scroll or AJAX product loading
    $(document.body).on('post-load', function() {
        // Reinitialize wishlist buttons after new products are loaded
        WooCustom.initWishlistButtons();
    });

})(jQuery);
