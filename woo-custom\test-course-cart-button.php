<?php
/**
 * Test file for Course Cart Button functionality
 * 
 * This file helps test the hide/show add to cart button functionality
 * for course products based on order completion status.
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Test Course Cart Button Functionality
 */
class WooCustom_Test_Course_Cart_Button {
    
    public function __construct() {
        add_action('init', array($this, 'run_tests'));
    }
    
    /**
     * Run all tests
     */
    public function run_tests() {
        // Only run tests if WP_DEBUG is enabled and user is admin
        if (!defined('WP_DEBUG') || !WP_DEBUG || !current_user_can('manage_options')) {
            return;
        }
        
        // Only run tests if requested via URL parameter
        if (!isset($_GET['test_course_cart_button'])) {
            return;
        }
        
        echo '<div style="background: #f0f0f0; padding: 20px; margin: 20px; border: 1px solid #ccc;">';
        echo '<h2>Woo Custom - Course Cart Button Test Results</h2>';
        
        $this->test_course_info_class_exists();
        $this->test_user_has_completed_order_method();
        $this->test_get_course_id_by_product_method();
        $this->test_get_current_product_id_method();
        $this->test_javascript_functionality();
        $this->test_css_styles();
        
        echo '</div>';
    }
    
    /**
     * Test if Course Info class exists and methods are available
     */
    private function test_course_info_class_exists() {
        echo '<h3>1. Class and Method Existence Test</h3>';
        
        if (class_exists('WooCustom_Course_Info')) {
            echo '<p style="color: green;">✓ WooCustom_Course_Info class exists</p>';
            
            $course_info = WooCustom_Course_Info::instance();
            
            if (method_exists($course_info, 'enqueue_course_scripts')) {
                echo '<p style="color: green;">✓ enqueue_course_scripts method exists</p>';
            } else {
                echo '<p style="color: red;">✗ enqueue_course_scripts method missing</p>';
            }
            
            if (method_exists($course_info, 'hide_add_to_cart_for_completed_courses')) {
                echo '<p style="color: green;">✓ hide_add_to_cart_for_completed_courses method exists</p>';
            } else {
                echo '<p style="color: red;">✗ hide_add_to_cart_for_completed_courses method missing</p>';
            }

            if (method_exists($course_info, 'get_current_product_id')) {
                echo '<p style="color: green;">✓ get_current_product_id method exists</p>';
            } else {
                echo '<p style="color: red;">✗ get_current_product_id method missing</p>';
            }
            
        } else {
            echo '<p style="color: red;">✗ WooCustom_Course_Info class not found</p>';
        }
    }
    
    /**
     * Test user_has_completed_order method (now checks paid orders)
     */
    private function test_user_has_completed_order_method() {
        echo '<h3>2. User Paid Order Test</h3>';

        if (!class_exists('WooCustom_Course_Info')) {
            echo '<p style="color: red;">✗ Cannot test - WooCustom_Course_Info class not found</p>';
            return;
        }

        $course_info = WooCustom_Course_Info::instance();

        // Use reflection to access private method for testing
        $reflection = new ReflectionClass($course_info);
        if ($reflection->hasMethod('user_has_completed_order')) {
            echo '<p style="color: green;">✓ user_has_completed_order method exists</p>';

            // Test with invalid parameters
            $method = $reflection->getMethod('user_has_completed_order');
            $method->setAccessible(true);

            $result = $method->invoke($course_info, 0, 0);
            if ($result === false) {
                echo '<p style="color: green;">✓ Method correctly returns false for invalid parameters</p>';
            } else {
                echo '<p style="color: orange;">⚠ Method behavior with invalid parameters: ' . var_export($result, true) . '</p>';
            }

            // Test WooCommerce paid statuses function
            if (function_exists('wc_get_is_paid_statuses')) {
                $paid_statuses = wc_get_is_paid_statuses();
                echo '<p style="color: green;">✓ WooCommerce paid statuses: ' . implode(', ', $paid_statuses) . '</p>';
            } else {
                echo '<p style="color: red;">✗ wc_get_is_paid_statuses function not found</p>';
            }

        } else {
            echo '<p style="color: red;">✗ user_has_completed_order method not found</p>';
        }
    }
    
    /**
     * Test get_course_id_by_product method
     */
    private function test_get_course_id_by_product_method() {
        echo '<h3>3. Get Course ID by Product Test</h3>';

        if (!class_exists('WooCustom_Course_Info')) {
            echo '<p style="color: red;">✗ Cannot test - WooCustom_Course_Info class not found</p>';
            return;
        }

        $course_info = WooCustom_Course_Info::instance();

        // Use reflection to access private method for testing
        $reflection = new ReflectionClass($course_info);
        if ($reflection->hasMethod('get_course_id_by_product')) {
            echo '<p style="color: green;">✓ get_course_id_by_product method exists</p>';

            // Test with invalid product ID
            $method = $reflection->getMethod('get_course_id_by_product');
            $method->setAccessible(true);

            $result = $method->invoke($course_info, 0);
            if ($result === false) {
                echo '<p style="color: green;">✓ Method correctly returns false for invalid product ID</p>';
            } else {
                echo '<p style="color: orange;">⚠ Method behavior with invalid product ID: ' . var_export($result, true) . '</p>';
            }

        } else {
            echo '<p style="color: red;">✗ get_course_id_by_product method not found</p>';
        }
    }

    /**
     * Test get_current_product_id method
     */
    private function test_get_current_product_id_method() {
        echo '<h3>4. Get Current Product ID Test</h3>';

        if (!class_exists('WooCustom_Course_Info')) {
            echo '<p style="color: red;">✗ Cannot test - WooCustom_Course_Info class not found</p>';
            return;
        }

        $course_info = WooCustom_Course_Info::instance();

        // Use reflection to access private method for testing
        $reflection = new ReflectionClass($course_info);
        if ($reflection->hasMethod('get_current_product_id')) {
            echo '<p style="color: green;">✓ get_current_product_id method exists</p>';

            // Test the method
            $method = $reflection->getMethod('get_current_product_id');
            $method->setAccessible(true);

            $result = $method->invoke($course_info);
            if ($result === false || is_numeric($result)) {
                echo '<p style="color: green;">✓ Method returns valid result: ' . var_export($result, true) . '</p>';
            } else {
                echo '<p style="color: orange;">⚠ Method behavior: ' . var_export($result, true) . '</p>';
            }

        } else {
            echo '<p style="color: red;">✗ get_current_product_id method not found</p>';
        }
    }
    
    /**
     * Test JavaScript functionality
     */
    private function test_javascript_functionality() {
        echo '<h3>5. JavaScript Functionality Test</h3>';
        
        // Check if woo-custom.js file exists
        $js_file = WOO_CUSTOM_PLUGIN_DIR . 'assets/js/woo-custom.js';
        if (file_exists($js_file)) {
            echo '<p style="color: green;">✓ woo-custom.js file exists</p>';
            
            $js_content = file_get_contents($js_file);
            
            if (strpos($js_content, 'initCourseCompletedCheck') !== false) {
                echo '<p style="color: green;">✓ initCourseCompletedCheck function found in JavaScript</p>';
            } else {
                echo '<p style="color: red;">✗ initCourseCompletedCheck function not found in JavaScript</p>';
            }
            
            if (strpos($js_content, 'hideAddToCartButton') !== false) {
                echo '<p style="color: green;">✓ hideAddToCartButton function found in JavaScript</p>';
            } else {
                echo '<p style="color: red;">✗ hideAddToCartButton function not found in JavaScript</p>';
            }
            
            if (strpos($js_content, '#sticky-scroll > button') !== false) {
                echo '<p style="color: green;">✓ Specific button selector "#sticky-scroll > button" found in JavaScript</p>';
            } else {
                echo '<p style="color: red;">✗ Specific button selector "#sticky-scroll > button" not found in JavaScript</p>';
            }
            
        } else {
            echo '<p style="color: red;">✗ woo-custom.js file not found</p>';
        }
    }
    
    /**
     * Test CSS styles
     */
    private function test_css_styles() {
        echo '<h3>6. CSS Styles Test</h3>';
        
        // Check if woo-custom.css file exists
        $css_file = WOO_CUSTOM_PLUGIN_DIR . 'assets/css/woo-custom.css';
        if (file_exists($css_file)) {
            echo '<p style="color: green;">✓ woo-custom.css file exists</p>';
            
            $css_content = file_get_contents($css_file);
            
            if (strpos($css_content, '.course-completed-notice') !== false) {
                echo '<p style="color: green;">✓ .course-completed-notice styles found in CSS</p>';
            } else {
                echo '<p style="color: red;">✗ .course-completed-notice styles not found in CSS</p>';
            }
            
            if (strpos($css_content, '.course-completed') !== false) {
                echo '<p style="color: green;">✓ .course-completed styles found in CSS</p>';
            } else {
                echo '<p style="color: red;">✗ .course-completed styles not found in CSS</p>';
            }
            
            if (strpos($css_content, '#sticky-scroll > button') !== false) {
                echo '<p style="color: green;">✓ Specific button selector "#sticky-scroll > button" found in CSS</p>';
            } else {
                echo '<p style="color: red;">✗ Specific button selector "#sticky-scroll > button" not found in CSS</p>';
            }
            
        } else {
            echo '<p style="color: red;">✗ woo-custom.css file not found</p>';
        }
    }
}

// Initialize test if in debug mode
if (defined('WP_DEBUG') && WP_DEBUG) {
    new WooCustom_Test_Course_Cart_Button();
}
