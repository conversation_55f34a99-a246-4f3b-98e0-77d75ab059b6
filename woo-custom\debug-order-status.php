<?php
/**
 * Debug Order Status for Course Products
 * 
 * This file helps debug order statuses for course products
 * Access via: your-site.com/?debug_order_status=1&product_id=123
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Debug Order Status Functionality
 */
class WooCustom_Debug_Order_Status {
    
    public function __construct() {
        add_action('init', array($this, 'handle_debug_request'));
    }
    
    /**
     * Handle debug request
     */
    public function handle_debug_request() {
        // Only run if debug parameter is set
        if (!isset($_GET['debug_order_status'])) {
            return;
        }
        
        // Only run if user is admin
        if (!current_user_can('manage_options')) {
            wp_die('Bu sayfaya erişim yetkiniz yok.');
        }
        
        $this->display_debug_info();
        exit;
    }
    
    /**
     * Display debug information
     */
    private function display_debug_info() {
        $product_id = isset($_GET['product_id']) ? intval($_GET['product_id']) : 0;
        $user_id = get_current_user_id();
        
        ?>
        <!DOCTYPE html>
        <html>
        <head>
            <title>Woo Custom - Order Status Debug</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                .debug-section { background: #f9f9f9; padding: 15px; margin: 15px 0; border-left: 4px solid #0073aa; }
                .success { color: green; }
                .error { color: red; }
                .warning { color: orange; }
                .info { color: blue; }
                table { border-collapse: collapse; width: 100%; margin: 10px 0; }
                th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                th { background-color: #f2f2f2; }
                .status-paid { background-color: #d4edda; }
                .status-unpaid { background-color: #f8d7da; }
            </style>
        </head>
        <body>
            <h1>Woo Custom - Sipariş Durumu Debug</h1>
            
            <div class="debug-section">
                <h2>Genel Bilgiler</h2>
                <p><strong>Mevcut Kullanıcı ID:</strong> <?php echo $user_id; ?></p>
                <p><strong>Kontrol Edilen Ürün ID:</strong> <?php echo $product_id; ?></p>
                <p><strong>Kullanıcı Giriş Yapmış:</strong> <?php echo $user_id ? 'Evet' : 'Hayır'; ?></p>
            </div>
            
            <?php if ($product_id): ?>
                <div class="debug-section">
                    <h2>Ürün Bilgileri</h2>
                    <?php
                    $product = wc_get_product($product_id);
                    if ($product) {
                        echo '<p><strong>Ürün Adı:</strong> ' . $product->get_name() . '</p>';
                        echo '<p><strong>Ürün Türü:</strong> ' . $product->get_type() . '</p>';
                        echo '<p><strong>Ürün Durumu:</strong> ' . $product->get_status() . '</p>';
                        
                        // Check if it's a course product
                        if (class_exists('WooCustom_Course_Info')) {
                            $course_info = WooCustom_Course_Info::instance();
                            $reflection = new ReflectionClass($course_info);
                            $method = $reflection->getMethod('get_course_id_by_product');
                            $method->setAccessible(true);
                            $course_id = $method->invoke($course_info, $product_id);
                            
                            echo '<p><strong>Kursa Bağlı:</strong> ' . ($course_id ? "Evet (Kurs ID: $course_id)" : 'Hayır') . '</p>';
                        }
                    } else {
                        echo '<p class="error">Ürün bulunamadı!</p>';
                    }
                    ?>
                </div>
            <?php endif; ?>
            
            <div class="debug-section">
                <h2>WooCommerce Sipariş Durumları</h2>
                <?php
                if (function_exists('wc_get_order_statuses')) {
                    $all_statuses = wc_get_order_statuses();
                    echo '<h3>Tüm Sipariş Durumları:</h3>';
                    echo '<ul>';
                    foreach ($all_statuses as $status => $label) {
                        echo '<li><strong>' . $status . '</strong>: ' . $label . '</li>';
                    }
                    echo '</ul>';
                }
                
                if (function_exists('wc_get_is_paid_statuses')) {
                    $paid_statuses = wc_get_is_paid_statuses();
                    echo '<h3>Ödeme Yapılmış Durumlar:</h3>';
                    echo '<ul>';
                    foreach ($paid_statuses as $status) {
                        echo '<li class="success">' . $status . '</li>';
                    }
                    echo '</ul>';
                }
                ?>
            </div>
            
            <?php if ($user_id): ?>
                <div class="debug-section">
                    <h2>Kullanıcının Siparişleri</h2>
                    <?php
                    $orders = wc_get_orders(array(
                        'customer_id' => $user_id,
                        'limit' => -1,
                    ));
                    
                    if ($orders) {
                        echo '<table>';
                        echo '<tr><th>Sipariş ID</th><th>Durum</th><th>Tarih</th><th>Ürünler</th><th>Ödeme Durumu</th></tr>';
                        
                        foreach ($orders as $order) {
                            $status = $order->get_status();
                            $paid_statuses = wc_get_is_paid_statuses();
                            $is_paid = in_array('wc-' . $status, $paid_statuses);
                            $row_class = $is_paid ? 'status-paid' : 'status-unpaid';
                            
                            echo '<tr class="' . $row_class . '">';
                            echo '<td>#' . $order->get_id() . '</td>';
                            echo '<td>' . $status . '</td>';
                            echo '<td>' . $order->get_date_created()->format('Y-m-d H:i:s') . '</td>';
                            echo '<td>';
                            
                            $items = $order->get_items();
                            foreach ($items as $item) {
                                $item_product_id = $item->get_product_id();
                                $item_product = $item->get_product();
                                $highlight = ($product_id && $item_product_id == $product_id) ? ' style="background-color: yellow;"' : '';
                                echo '<div' . $highlight . '>';
                                echo 'ID: ' . $item_product_id . ' - ';
                                echo ($item_product ? $item_product->get_name() : 'Ürün bulunamadı');
                                echo '</div>';
                            }
                            
                            echo '</td>';
                            echo '<td>' . ($is_paid ? '<span class="success">ÖDENDİ</span>' : '<span class="error">ÖDENMEDİ</span>') . '</td>';
                            echo '</tr>';
                        }
                        
                        echo '</table>';
                    } else {
                        echo '<p>Bu kullanıcının hiç siparişi yok.</p>';
                    }
                    ?>
                </div>
            <?php endif; ?>
            
            <?php if ($product_id && $user_id): ?>
                <div class="debug-section">
                    <h2>Ürün İçin Sipariş Kontrolü</h2>
                    <?php
                    if (class_exists('WooCustom_Course_Info')) {
                        $course_info = WooCustom_Course_Info::instance();
                        $reflection = new ReflectionClass($course_info);
                        $method = $reflection->getMethod('user_has_completed_order');
                        $method->setAccessible(true);
                        $has_paid_order = $method->invoke($course_info, $user_id, $product_id);
                        
                        echo '<p><strong>Bu ürün için ödeme yapılmış sipariş var mı?</strong> ';
                        if ($has_paid_order) {
                            echo '<span class="success">EVET - Sepete ekle butonu GİZLENMELİ</span>';
                        } else {
                            echo '<span class="error">HAYIR - Sepete ekle butonu GÖRÜNMELİ</span>';
                        }
                        echo '</p>';
                    }
                    ?>
                </div>
            <?php endif; ?>
            
            <div class="debug-section">
                <h2>Kullanım</h2>
                <p>Bu debug sayfasını kullanmak için URL'ye şu parametreleri ekleyin:</p>
                <ul>
                    <li><strong>debug_order_status=1</strong> - Debug sayfasını açar</li>
                    <li><strong>product_id=123</strong> - Kontrol edilecek ürün ID'si</li>
                </ul>
                <p><strong>Örnek:</strong> <code>your-site.com/?debug_order_status=1&product_id=123</code></p>
            </div>
            
        </body>
        </html>
        <?php
    }
}

// Initialize debug if in debug mode
if (defined('WP_DEBUG') && WP_DEBUG) {
    new WooCustom_Debug_Order_Status();
}
