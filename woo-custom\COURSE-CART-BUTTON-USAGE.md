# Kurs Sepete Ekle Butonu Gizleme Özelliği

Bu özellik, kursa bağlanan WooCommerce ürünlerinde sipariş durumuna göre "Sepete Ekle" butonunu otomatik olarak gizler/gösterir.

## Nasıl Çalışır

### 1. Otomatik Algılama
- E<PERSON>nti, ür<PERSON>n sayfasında o ürünün bir kursa bağlı olup olmadığını kontrol eder
- Kullanıcının o ürün için **ödeme yapılmış** siparişi olup olmadığını kontrol eder
- Eğer ödeme yapılmış sipariş varsa, "Sepete Ekle" butonunu gizler

### 1.1. Ödeme Yapılmış Sipariş Durumları
WooCommerce'de ödeme yapılmış sayılan durumlar:
- **`processing` (İşleniyor)** - <PERSON><PERSON><PERSON>, stok düşürüldü
- **`completed` (Tamamlandı)** - Sipariş tamamen tamamlandı

Bu iki durumdan herhangi birinde olan siparişler için buton gizlenir.

### 2. Hedeflenen Buton Seçicileri
Eklenti aşağıdaki CSS seçicilerini kullanarak butonları gizler:

**Birincil Hedef (Sizin Belirttiğiniz):**
```css
#sticky-scroll > button
```

**Yedek Hedefler (Uyumluluk için):**
```css
.single_add_to_cart_button
button[name="add-to-cart"]
.cart button[type="submit"]
```

### 3. Kullanıcı Bildirimi
Buton gizlendiğinde, kullanıcıya şu mesaj gösterilir:
```
Bu kursu zaten satın aldınız!
Ödemeniz alınmış ve kursa erişiminiz bulunmaktadır.
```

## Teknik Detaylar

### PHP Tarafı
- `WooCustom_Course_Info` sınıfında yeni metodlar eklendi
- `user_has_completed_order()` metodu sipariş durumunu kontrol eder
- `enqueue_course_scripts()` metodu gerekli JavaScript'i yükler
- `hide_add_to_cart_for_completed_courses()` metodu yedek gizleme işlemi yapar

### JavaScript Tarafı
- `initCourseCompletedCheck()` fonksiyonu sayfa yüklendiğinde çalışır
- `hideAddToCartButton()` fonksiyonu butonları gizler
- `MutationObserver` ile dinamik içerik değişikliklerini izler

### CSS Tarafı
- `.course-completed-notice` sınıfı bildirim stillerini tanımlar
- `.course-completed` sınıfı gizlenmiş buton durumunu yönetir

## Test Etme

### 1. Manuel Test
1. Kursa bağlı bir WooCommerce ürünü oluşturun
2. O ürünü satın alın ve siparişi **"İşleniyor" (processing)** veya **"Tamamlandı" (completed)** durumuna getirin
3. Ürün sayfasını ziyaret edin
4. "Sepete Ekle" butonunun gizlendiğini ve bildirim mesajının göründüğünü kontrol edin

**Not:** Sipariş durumu "Beklemede" (pending), "Başarısız" (failed) veya "İptal Edildi" (cancelled) ise buton gizlenmez.

### 2. Otomatik Test
Debug modunda aşağıdaki URL'yi ziyaret edin:
```
your-site.com/?test_course_cart_button=1
```

Bu test şunları kontrol eder:
- Sınıf ve metodların varlığı
- JavaScript fonksiyonlarının varlığı
- CSS stillerinin varlığı
- Seçici hedeflerinin doğruluğu

## Sorun Giderme

### Buton Gizlenmiyor
1. **Kurs Bağlantısını Kontrol Edin:**
   - Ürünün gerçekten bir kursa bağlı olduğundan emin olun
   - `_tutor_course_product_id` meta değerini kontrol edin

2. **Sipariş Durumunu Kontrol Edin:**
   - Siparişin "processing" (işleniyor) veya "completed" (tamamlandı) durumunda olduğundan emin olun
   - Kullanıcının giriş yapmış olduğundan emin olun
   - Sipariş durumunu WooCommerce admin panelinden kontrol edin

3. **JavaScript Hatalarını Kontrol Edin:**
   - Tarayıcı konsolunda hata olup olmadığını kontrol edin
   - `woo-custom.js` dosyasının yüklendiğinden emin olun

4. **CSS Seçicilerini Kontrol Edin:**
   - Temanızın farklı seçiciler kullanıp kullanmadığını kontrol edin
   - Gerekirse CSS'e ek seçiciler ekleyin

### Yanlış Buton Gizleniyor
1. **Seçici Spesifikliğini Artırın:**
   ```css
   /* Daha spesifik seçici kullanın */
   .single-product #sticky-scroll > button.add-to-cart
   ```

2. **JavaScript Seçicilerini Güncelleyin:**
   ```javascript
   // Daha spesifik seçici
   $("#sticky-scroll > button.your-specific-class").hide();
   ```

### Bildirim Mesajı Görünmüyor
1. **CSS Stillerini Kontrol Edin:**
   - `.course-completed-notice` stillerinin yüklendiğinden emin olun
   - Z-index değerlerini kontrol edin

2. **JavaScript Ekleme Konumunu Kontrol Edin:**
   - Mesajın doğru DOM elementine eklendiğinden emin olun
   - `.summary .cart` elementinin varlığını kontrol edin

## Özelleştirme

### Bildirim Mesajını Değiştirme
```php
// functions.php dosyanıza ekleyin
add_filter('woo_custom_course_completed_message', function($message) {
    return 'Özel mesajınız burada!';
});
```

### Ek Buton Seçicileri Ekleme
```javascript
// Tema dosyanıza ekleyin
jQuery(document).ready(function($) {
    // Ek seçiciler
    $(".your-custom-button-class").hide();
    $("#your-custom-button-id").hide();
});
```

### CSS Stillerini Özelleştirme
```css
/* Tema CSS dosyanıza ekleyin */
.course-completed-notice {
    background: #your-color !important;
    color: #your-text-color !important;
    /* Diğer stilleriniz */
}
```

## Güvenlik

- Tüm AJAX istekleri nonce ile korunur
- Kullanıcı yetkileri kontrol edilir
- SQL injection koruması vardır
- XSS koruması uygulanır

## Performans

- Sadece gerekli sayfalarda çalışır (tek ürün sayfaları)
- Minimal JavaScript kodu
- Optimize edilmiş veritabanı sorguları
- Önbellekleme dostu yapı

## Uyumluluk

- WordPress 6.0+
- WooCommerce 8.0+
- PHP 7.4+
- Tüm modern tarayıcılar
- Mobil cihazlar
- Tutor LMS (opsiyonel)
