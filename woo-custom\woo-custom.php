<?php
/**
 * Plugin Name: Woo Custom
 * Plugin URI: https://example.com/woo-custom
 * Description: Custom WooCommerce enhancements including Wishlist, My Reviews functionality for the My Account page, and Tutor LMS course information integration.
 * Version: 1.1.0
 * Author: Your Name
 * Author URI: https://example.com
 * Text Domain: woo-custom
 * Domain Path: /languages
 * Requires at least: 6.0
 * Tested up to: 6.4
 * Requires PHP: 7.4
 * WC requires at least: 8.0
 * WC tested up to: 9.9
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('WOO_CUSTOM_VERSION', '1.1.0');
define('WOO_CUSTOM_PLUGIN_FILE', __FILE__);
define('WOO_CUSTOM_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('WOO_CUSTOM_PLUGIN_URL', plugin_dir_url(__FILE__));
define('WOO_CUSTOM_PLUGIN_BASENAME', plugin_basename(__FILE__));

/**
 * Main Woo Custom Plugin Class
 */
class WooCustom {
    
    /**
     * Single instance of the class
     */
    private static $instance = null;
    
    /**
     * Get single instance
     */
    public static function instance() {
        if (is_null(self::$instance)) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        add_action('plugins_loaded', array($this, 'init'));
    }
    
    /**
     * Initialize the plugin
     */
    public function init() {
        // Check if WooCommerce is active
        if (!$this->is_woocommerce_active()) {
            add_action('admin_notices', array($this, 'woocommerce_missing_notice'));
            return;
        }
        
        // Load plugin files
        $this->includes();
        
        // Initialize hooks
        $this->init_hooks();
        
        // Load text domain
        add_action('init', array($this, 'load_textdomain'));
    }
    
    /**
     * Check if WooCommerce is active
     */
    private function is_woocommerce_active() {
        return class_exists('WooCommerce');
    }
    
    /**
     * WooCommerce missing notice
     */
    public function woocommerce_missing_notice() {
        ?>
        <div class="notice notice-error">
            <p><?php esc_html_e('Woo Custom requires WooCommerce to be installed and active.', 'woo-custom'); ?></p>
        </div>
        <?php
    }
    
    /**
     * Include required files
     */
    private function includes() {
        // Include core classes
        require_once WOO_CUSTOM_PLUGIN_DIR . 'includes/class-woo-custom-wishlist.php';
        require_once WOO_CUSTOM_PLUGIN_DIR . 'includes/class-woo-custom-reviews.php';
        require_once WOO_CUSTOM_PLUGIN_DIR . 'includes/class-woo-custom-account.php';
        require_once WOO_CUSTOM_PLUGIN_DIR . 'includes/class-woo-custom-ajax.php';
        require_once WOO_CUSTOM_PLUGIN_DIR . 'includes/class-woo-custom-course-info.php';

        // Include test files in development (optional)
        // Uncomment the lines below if you need to debug course info integration
        if (defined('WP_DEBUG') && WP_DEBUG) {
            require_once WOO_CUSTOM_PLUGIN_DIR . 'debug-course-info.php';
            require_once WOO_CUSTOM_PLUGIN_DIR . 'test-tab-visibility.php';
            require_once WOO_CUSTOM_PLUGIN_DIR . 'test-course-cart-button.php';
            require_once WOO_CUSTOM_PLUGIN_DIR . 'debug-order-status.php';
        }
    }
    
    /**
     * Initialize hooks
     */
    private function init_hooks() {
        // Activation and deactivation hooks
        register_activation_hook(WOO_CUSTOM_PLUGIN_FILE, array($this, 'activate'));
        register_deactivation_hook(WOO_CUSTOM_PLUGIN_FILE, array($this, 'deactivate'));
        
        // Initialize components
        WooCustom_Account::instance();
        WooCustom_Wishlist::instance();
        WooCustom_Reviews::instance();
        WooCustom_Ajax::instance();
        WooCustom_Course_Info::instance();
        
        // Enqueue scripts and styles
        add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));
        add_action('admin_enqueue_scripts', array($this, 'admin_enqueue_scripts'));
    }
    
    /**
     * Load text domain
     */
    public function load_textdomain() {
        load_plugin_textdomain('woo-custom', false, dirname(WOO_CUSTOM_PLUGIN_BASENAME) . '/languages');
    }
    
    /**
     * Enqueue frontend scripts and styles
     */
    public function enqueue_scripts() {
        wp_enqueue_style(
            'woo-custom-style',
            WOO_CUSTOM_PLUGIN_URL . 'assets/css/woo-custom.css',
            array(),
            WOO_CUSTOM_VERSION
        );
        
        wp_enqueue_script(
            'woo-custom-script',
            WOO_CUSTOM_PLUGIN_URL . 'assets/js/woo-custom.js',
            array('jquery'),
            WOO_CUSTOM_VERSION,
            true
        );
        
        // Localize script for AJAX
        wp_localize_script('woo-custom-script', 'woo_custom_ajax', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('woo_custom_nonce'),
            'i18n' => array(
                'added_to_wishlist' => __('İstek listesine eklendi', 'woo-custom'),
                'removed_from_wishlist' => __('İstek listesinden çıkarıldı', 'woo-custom'),
                'error' => __('Bir hata oluştu', 'woo-custom'),
                'confirm_remove' => __('Bu ürünü istek listenizden çıkarmak istediğinizden emin misiniz?', 'woo-custom'),
                'login_required' => __('İstek listesini kullanmak için giriş yapmalısınız', 'woo-custom'),
            )
        ));
    }
    
    /**
     * Enqueue admin scripts and styles
     */
    public function admin_enqueue_scripts() {
        // Admin styles if needed
    }
    
    /**
     * Plugin activation
     */
    public function activate() {
        // Create database tables
        $this->create_tables();
        
        // Flush rewrite rules
        flush_rewrite_rules();
    }
    
    /**
     * Plugin deactivation
     */
    public function deactivate() {
        // Flush rewrite rules
        flush_rewrite_rules();
    }
    
    /**
     * Create database tables
     */
    private function create_tables() {
        global $wpdb;

        $charset_collate = $wpdb->get_charset_collate();

        // Wishlist table
        $table_name = $wpdb->prefix . 'woo_custom_wishlist';

        $sql = "CREATE TABLE $table_name (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            user_id bigint(20) NOT NULL,
            item_id bigint(20) NOT NULL,
            item_type varchar(20) NOT NULL DEFAULT 'product',
            date_added datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY user_item (user_id, item_id, item_type),
            KEY user_id (user_id),
            KEY item_id (item_id),
            KEY item_type (item_type)
        ) $charset_collate;";

        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        $result = dbDelta($sql);

        // Verify table was created
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") == $table_name;

        if (!$table_exists) {
            // Log error if debug mode is enabled
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('Woo Custom: Failed to create wishlist table: ' . $table_name);
                error_log('Woo Custom: dbDelta result: ' . print_r($result, true));
                error_log('Woo Custom: Last DB error: ' . $wpdb->last_error);
            }

            // Try alternative table creation method
            $wpdb->query($sql);

            // Check again
            $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") == $table_name;

            if (!$table_exists && defined('WP_DEBUG') && WP_DEBUG) {
                error_log('Woo Custom: Alternative table creation also failed');
            }
        } else {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('Woo Custom: Wishlist table created successfully: ' . $table_name);
            }
        }

        // Migrate existing data if needed
        $this->migrate_wishlist_table();
    }

    /**
     * Migrate existing wishlist table to support courses
     */
    private function migrate_wishlist_table() {
        global $wpdb;
        $table_name = $wpdb->prefix . 'woo_custom_wishlist';

        // Check if table exists
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") == $table_name;
        if (!$table_exists) {
            return;
        }

        // Check if migration is needed (if item_type column doesn't exist)
        $columns = $wpdb->get_results("SHOW COLUMNS FROM $table_name");
        $has_item_type = false;
        $has_product_id = false;

        foreach ($columns as $column) {
            if ($column->Field === 'item_type') {
                $has_item_type = true;
            }
            if ($column->Field === 'product_id') {
                $has_product_id = true;
            }
        }

        // If we have product_id but not item_type, we need to migrate
        if ($has_product_id && !$has_item_type) {
            // Add new columns
            $wpdb->query("ALTER TABLE $table_name ADD COLUMN item_id bigint(20) NOT NULL DEFAULT 0 AFTER user_id");
            $wpdb->query("ALTER TABLE $table_name ADD COLUMN item_type varchar(20) NOT NULL DEFAULT 'product' AFTER item_id");

            // Copy product_id to item_id and set item_type to 'product'
            $wpdb->query("UPDATE $table_name SET item_id = product_id, item_type = 'product'");

            // Drop old unique key and create new one
            $wpdb->query("ALTER TABLE $table_name DROP INDEX user_product");
            $wpdb->query("ALTER TABLE $table_name ADD UNIQUE KEY user_item (user_id, item_id, item_type)");

            // Add new indexes
            $wpdb->query("ALTER TABLE $table_name ADD KEY item_id (item_id)");
            $wpdb->query("ALTER TABLE $table_name ADD KEY item_type (item_type)");

            // Drop product_id column
            $wpdb->query("ALTER TABLE $table_name DROP COLUMN product_id");

            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('Woo Custom: Wishlist table migrated to support courses');
            }
        }
    }

    /**
     * Check if database table exists and create if needed
     */
    public function ensure_table_exists() {
        global $wpdb;
        $table_name = $wpdb->prefix . 'woo_custom_wishlist';
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") == $table_name;

        if (!$table_exists) {
            $this->create_tables();
        }

        return $wpdb->get_var("SHOW TABLES LIKE '$table_name'") == $table_name;
    }
}

/**
 * Initialize the plugin
 */
function woo_custom() {
    return WooCustom::instance();
}

// Start the plugin
woo_custom();
